# AfyaSecure - Cloud-Native Health Management System

AfyaSecure is a comprehensive, secure, and scalable health management system built with modern technologies to serve patients, doctors, and healthcare administrators.

## 📁 Project Structure

```
afyasecure/
├── frontend/          # React.js + TypeScript frontend
│   ├── src/          # Source code
│   ├── components/   # Reusable UI components
│   ├── public/       # Static assets
│   └── package.json  # Frontend dependencies
├── backend/          # Node.js + Express backend
│   ├── config/       # Database and logger configuration
│   ├── middleware/   # Express middleware
│   ├── models/       # Mongoose models
│   ├── routes/       # API routes
│   ├── uploads/      # File upload directory
│   ├── logs/         # Application logs
│   └── package.json  # Backend dependencies
├── README.md         # This file
└── .gitignore        # Git ignore rules
```

## 🏗️ Architecture

### Frontend
- **React.js 18** with TypeScript
- **Tailwind CSS** for responsive, mobile-first design
- **React Router** for client-side routing
- **Axios** for API communication with automatic token refresh
- **Context API** for state management

### Backend
- **Node.js 18+** with Express.js
- **MongoDB Atlas** for document-based data storage
- **JWT Authentication** with refresh tokens
- **Mongoose** for MongoDB object modeling
- **Winston** for structured logging

### Security Features
- HTTPS enforcement
- JWT with automatic refresh mechanism
- Role-based access control (RBAC)
- Input validation and sanitization
- Rate limiting
- CSRF protection
- Bcrypt password hashing
- Encrypted sensitive data storage
- Immutable activity logs

## 🚀 Features

### User Management
- **Multi-role system**: Patient, Doctor, Admin
- **Secure authentication** with JWT tokens
- **eVerification system** with cryptographically signed QR codes
- **Profile management** with photo uploads

### Patient Records
- **Complete CRUD operations** on patient profiles
- **Medical history tracking** with encrypted storage
- **Visit notes and prescriptions** management
- **Document uploads** (lab results, scans) to AWS S3
- **Role-based access control** for data privacy

### Appointment System
- **Booking, rescheduling, and cancellation** for patients
- **Calendar view** for doctors with timezone support
- **Status management** (scheduled, completed, cancelled)
- **Notification system** for reminders

### Health Facility Directory
- **Comprehensive facility listings** with services
- **Location-based search** and filtering
- **Doctor associations** and availability
- **Pagination and advanced search**

### Billing Module
- **Itemized invoice generation** with timestamps
- **Digital approval signatures**
- **Payment gateway integration** ready
- **Billing history and reports**

### Admin Dashboard
- **Platform statistics** and analytics
- **User management** and verification
- **System health monitoring**
- **Activity log analysis**

### Activity Logging
- **Immutable audit trail** for all user actions
- **Append-only design** in MongoDB
- **IP address and user agent tracking**
- **Secure backup mechanisms**

## 📋 Prerequisites

- Node.js 18+ and npm
- MongoDB Atlas account
- Git (optional, for version control)

## 🛠️ Installation

### Backend Setup (Node.js)

1. **Navigate to backend directory**
\`\`\`bash
cd backend
\`\`\`

2. **Install dependencies**
\`\`\`bash
npm install
\`\`\`

3. **Environment configuration**
\`\`\`bash
cp .env.example .env
# Edit .env with your MongoDB Atlas credentials
\`\`\`

4. **Start the development server**
\`\`\`bash
# Development mode with auto-restart
npm run dev

# Production mode
npm start
\`\`\`

The backend server will start on `http://localhost:3000`

### Frontend Setup

1. **Navigate to frontend directory**
\`\`\`bash
cd ../frontend
\`\`\`

2. **Install dependencies**
\`\`\`bash
npm install
\`\`\`

3. **Environment configuration**
\`\`\`bash
cp .env.example .env
# Set REACT_APP_API_URL to your backend URL
\`\`\`

4. **Start development server**
\`\`\`bash
npm start
\`\`\`

## 🔧 Configuration

### MongoDB Atlas Setup

1. Create a MongoDB Atlas cluster
2. Configure network access and database user
3. Update connection string in `.env`

### AWS S3 Configuration

1. Create an S3 bucket for file storage
2. Configure IAM user with appropriate permissions
3. Update AWS credentials in `.env`

### JWT Configuration

\`\`\`env
JWT_SECRET=your-256-bit-secret
JWT_TTL=60  # Token expiry in minutes
JWT_REFRESH_TTL=20160  # Refresh token expiry in minutes
\`\`\`

## 📚 API Documentation

### Authentication Endpoints

\`\`\`
POST /api/v1/auth/login
POST /api/v1/auth/register
POST /api/v1/auth/refresh
POST /api/v1/auth/logout
GET  /api/v1/auth/me
\`\`\`

### Patient Management

\`\`\`
GET    /api/v1/patients
POST   /api/v1/patients
GET    /api/v1/patients/{id}
PUT    /api/v1/patients/{id}
POST   /api/v1/patients/{id}/visit-notes
POST   /api/v1/patients/{id}/prescriptions
POST   /api/v1/patients/{id}/documents
\`\`\`

### Appointments

\`\`\`
GET    /api/v1/appointments
POST   /api/v1/appointments
GET    /api/v1/appointments/{id}
PUT    /api/v1/appointments/{id}
PATCH  /api/v1/appointments/{id}/status
GET    /api/v1/appointments/calendar/{date}
\`\`\`

### Facilities

\`\`\`
GET    /api/v1/facilities
GET    /api/v1/facilities/{id}
GET    /api/v1/facilities/search/{query}
\`\`\`

### Billing

\`\`\`
GET    /api/v1/billing
POST   /api/v1/billing
GET    /api/v1/billing/{id}
POST   /api/v1/billing/{id}/approve
GET    /api/v1/billing/{id}/invoice
\`\`\`

## 🔒 Security Considerations

### Data Protection
- All sensitive patient data is encrypted at rest
- HTTPS enforced for all communications
- JWT tokens with short expiry and refresh mechanism
- Role-based access control for all endpoints

### Input Validation
- Server-side validation for all inputs
- SQL injection prevention through parameterized queries
- XSS protection with input sanitization
- File upload restrictions and virus scanning

### Rate Limiting
- API rate limiting to prevent abuse
- Login attempt limiting with temporary lockouts
- Request size limitations

### Monitoring
- Comprehensive activity logging
- Real-time security alerts
- Failed authentication tracking
- Suspicious activity detection

## 🚀 Deployment

### Production Environment

1. **Server Requirements**
   - Ubuntu 20.04+ or CentOS 8+
   - Nginx or Apache web server
   - PHP 8.2+ with required extensions
   - Node.js 18+ for frontend build
   - SSL certificate for HTTPS

2. **Database Setup**
   - MongoDB Atlas production cluster
   - Proper indexing for performance
   - Regular automated backups

3. **File Storage**
   - AWS S3 with proper IAM policies
   - CloudFront CDN for global distribution
   - Backup and versioning enabled

4. **Monitoring**
   - Application performance monitoring
   - Error tracking and alerting
   - Security monitoring and logging

### Docker Deployment

\`\`\`dockerfile
# Backend Dockerfile
FROM php:8.2-fpm
RUN docker-php-ext-install pdo pdo_mysql
COPY . /var/www/html
WORKDIR /var/www/html
RUN composer install --optimize-autoloader --no-dev
\`\`\`

\`\`\`dockerfile
# Frontend Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
FROM nginx:alpine
COPY --from=0 /app/build /usr/share/nginx/html
\`\`\`

## 🧪 Testing

### Backend Testing
\`\`\`bash
php artisan test
\`\`\`

### Frontend Testing
\`\`\`bash
npm test
npm run test:coverage
\`\`\`

### API Testing
Use the provided Postman collection for comprehensive API testing.

## 📊 Performance Optimization

- Database indexing for frequently queried fields
- Redis caching for session data and frequently accessed information
- Image optimization and CDN usage
- Lazy loading for frontend components
- API response compression

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Documentation: https://docs.afyasecure.com
- Issues: GitHub Issues page

## 🔄 Version History

- **v1.0.0** - Initial release with core features
- **v1.1.0** - Enhanced security and performance improvements
- **v1.2.0** - Mobile app integration and advanced analytics

---

Built with ❤️ for better healthcare management.
