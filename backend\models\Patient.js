import mongoose from 'mongoose';

const medicalHistorySchema = new mongoose.Schema({
    condition: {
        type: String,
        required: [true, 'Medical condition is required'],
        trim: true
    },
    diagnosisDate: {
        type: Date,
        required: [true, 'Diagnosis date is required']
    },
    treatment: {
        type: String,
        trim: true
    },
    doctor: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Doctor is required']
    },
    notes: {
        type: String,
        trim: true
    },
    status: {
        type: String,
        enum: ['active', 'resolved', 'chronic'],
        default: 'active'
    }
}, {
    timestamps: true
});

const emergencyContactSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Emergency contact name is required'],
        trim: true
    },
    relationship: {
        type: String,
        required: [true, 'Relationship is required'],
        trim: true
    },
    phone: {
        type: String,
        required: [true, 'Emergency contact phone is required'],
        trim: true,
        match: [/^\+?[\d\s-()]+$/, 'Please enter a valid phone number']
    },
    email: {
        type: String,
        trim: true,
        lowercase: true,
        match: [
            /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
            'Please enter a valid email address'
        ]
    }
});

const patientSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User reference is required'],
        unique: true
    },
    patientId: {
        type: String,
        unique: true,
        required: [true, 'Patient ID is required']
    },
    bloodType: {
        type: String,
        enum: {
            values: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
            message: 'Blood type must be one of: A+, A-, B+, B-, AB+, AB-, O+, O-'
        }
    },
    allergies: [{
        allergen: {
            type: String,
            required: true,
            trim: true
        },
        severity: {
            type: String,
            enum: ['mild', 'moderate', 'severe'],
            default: 'mild'
        },
        reaction: {
            type: String,
            trim: true
        }
    }],
    medications: [{
        name: {
            type: String,
            required: true,
            trim: true
        },
        dosage: {
            type: String,
            required: true,
            trim: true
        },
        frequency: {
            type: String,
            required: true,
            trim: true
        },
        prescribedBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        startDate: {
            type: Date,
            required: true
        },
        endDate: {
            type: Date
        },
        isActive: {
            type: Boolean,
            default: true
        }
    }],
    medicalHistory: [medicalHistorySchema],
    emergencyContacts: [emergencyContactSchema],
    insurance: {
        provider: {
            type: String,
            trim: true
        },
        policyNumber: {
            type: String,
            trim: true
        },
        groupNumber: {
            type: String,
            trim: true
        },
        expiryDate: {
            type: Date
        }
    },
    primaryDoctor: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    height: {
        value: {
            type: Number,
            min: [0, 'Height must be positive']
        },
        unit: {
            type: String,
            enum: ['cm', 'ft'],
            default: 'cm'
        }
    },
    weight: {
        value: {
            type: Number,
            min: [0, 'Weight must be positive']
        },
        unit: {
            type: String,
            enum: ['kg', 'lbs'],
            default: 'kg'
        }
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Virtual for BMI calculation
patientSchema.virtual('bmi').get(function() {
    if (!this.height?.value || !this.weight?.value) return null;
    
    let heightInM = this.height.value;
    let weightInKg = this.weight.value;
    
    // Convert height to meters if in feet
    if (this.height.unit === 'ft') {
        heightInM = this.height.value * 0.3048;
    } else {
        heightInM = this.height.value / 100;
    }
    
    // Convert weight to kg if in lbs
    if (this.weight.unit === 'lbs') {
        weightInKg = this.weight.value * 0.453592;
    }
    
    const bmi = weightInKg / (heightInM * heightInM);
    return Math.round(bmi * 10) / 10;
});

// Virtual for age calculation
patientSchema.virtual('age').get(function() {
    if (!this.user?.dateOfBirth) return null;
    
    const today = new Date();
    const birthDate = new Date(this.user.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    
    return age;
});

// Indexes for better query performance
patientSchema.index({ patientId: 1 });
patientSchema.index({ user: 1 });
patientSchema.index({ primaryDoctor: 1 });
patientSchema.index({ isActive: 1 });
patientSchema.index({ createdAt: -1 });

// Pre-save middleware to generate patient ID
patientSchema.pre('save', async function(next) {
    if (!this.patientId) {
        const count = await this.constructor.countDocuments();
        this.patientId = `PAT${String(count + 1).padStart(6, '0')}`;
    }
    next();
});

// Static method to find by patient ID
patientSchema.statics.findByPatientId = function(patientId) {
    return this.findOne({ patientId }).populate('user primaryDoctor');
};

export default mongoose.model('Patient', patientSchema);
