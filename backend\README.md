# AfyaSecure Node.js - Health Management System

A modern Node.js application with MongoDB Atlas integration for comprehensive healthcare management.

## 🚀 Features

- **Node.js & Express** - Fast, scalable backend
- **MongoDB Atlas** - Cloud database with advanced querying
- **JWT Authentication** - Secure token-based authentication
- **Role-based Access Control** - <PERSON><PERSON>, Doctor, Nurse, Receptionist, Patient roles
- **RESTful API** - Clean, well-documented endpoints
- **Input Validation** - Comprehensive request validation
- **Error Handling** - Structured error responses
- **Logging** - Advanced logging with Winston
- **Security** - Helmet, CORS, rate limiting
- **Health Monitoring** - Built-in health checks

## 📋 Requirements

- Node.js 18.0.0 or higher
- npm 8.0.0 or higher
- MongoDB Atlas account

## 🛠️ Installation

### 1. Clone and Install Dependencies

```bash
cd nodejs-app
npm install
```

### 2. Configure Environment

1. Copy `.env.example` to `.env` (if needed)
2. Update your MongoDB Atlas connection string in `.env`:

```env
MONGODB_URI="mongodb+srv://username:<EMAIL>/afyasecure?retryWrites=true&w=majority"
```

### 3. Set Up MongoDB Atlas

1. Create a MongoDB Atlas account at https://cloud.mongodb.com
2. Create a new cluster
3. Create a database user
4. Whitelist your IP address (or use 0.0.0.0/0 for development)
5. Get your connection string and update the `.env` file

### 4. Start the Application

```bash
# Development mode with auto-restart
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3000`

## 🔧 Configuration

### Environment Variables

Key environment variables in `.env`:

```env
# Application
NODE_ENV=development
PORT=3000
APP_NAME="AfyaSecure Node.js"

# Database
MONGODB_URI="your-mongodb-atlas-connection-string"
MONGODB_DATABASE=afyasecure

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# CORS
CORS_ORIGIN=http://localhost:3000,http://127.0.0.1:3000
```

## 📚 API Endpoints

### Authentication
```
POST   /api/v1/auth/register      # Register new user
POST   /api/v1/auth/login         # Login user
GET    /api/v1/auth/me            # Get current user
PUT    /api/v1/auth/me            # Update profile
PUT    /api/v1/auth/change-password # Change password
POST   /api/v1/auth/logout        # Logout
```

### Users (Admin/Doctor only)
```
GET    /api/v1/users              # Get all users
GET    /api/v1/users/:id          # Get user by ID
POST   /api/v1/users              # Create user (admin only)
PUT    /api/v1/users/:id          # Update user (admin only)
DELETE /api/v1/users/:id          # Delete user (admin only)
GET    /api/v1/users/stats/overview # User statistics
```

### Patients
```
GET    /api/v1/patients           # Get all patients
GET    /api/v1/patients/:id       # Get patient by ID
POST   /api/v1/patients           # Create patient
PUT    /api/v1/patients/:id       # Update patient
POST   /api/v1/patients/:id/medical-history # Add medical history
GET    /api/v1/patients/stats/overview # Patient statistics
```

### Health Check
```
GET    /health                    # Basic health check
GET    /health/detailed           # Detailed health check
GET    /health/ready              # Readiness probe
GET    /health/live               # Liveness probe
```

## 🔐 Authentication & Authorization

### User Roles
- **Admin**: Full system access
- **Doctor**: Patient management, user viewing
- **Nurse**: Patient management
- **Receptionist**: Patient registration and basic management
- **Patient**: Limited access to own data

### JWT Token Usage

Include the JWT token in the Authorization header:

```bash
Authorization: Bearer <your-jwt-token>
```

## 📝 API Usage Examples

### Register a User
```bash
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Dr. John Doe",
    "email": "<EMAIL>",
    "password": "SecurePass123",
    "role": "doctor",
    "phone": "+**********",
    "department": "Cardiology",
    "specialization": "Interventional Cardiology"
  }'
```

### Login
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123"
  }'
```

### Create a Patient
```bash
curl -X POST http://localhost:3000/api/v1/patients \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{
    "firstName": "Jane",
    "lastName": "Smith",
    "dateOfBirth": "1990-05-15",
    "gender": "female",
    "email": "<EMAIL>",
    "phone": "+**********",
    "address": {
      "street": "123 Main St",
      "city": "Nairobi",
      "state": "Nairobi",
      "country": "Kenya"
    }
  }'
```

### Search Patients
```bash
curl "http://localhost:3000/api/v1/patients?search=jane&page=1&limit=10" \
  -H "Authorization: Bearer <your-jwt-token>"
```

## 🗂️ Project Structure

```
nodejs-app/
├── config/                 # Configuration files
│   ├── database.js        # MongoDB connection
│   └── logger.js          # Winston logger setup
├── middleware/            # Express middleware
│   ├── auth.js           # Authentication middleware
│   ├── errorHandler.js   # Error handling
│   └── notFound.js       # 404 handler
├── models/               # Mongoose models
│   ├── User.js          # User model
│   └── Patient.js       # Patient model
├── routes/              # API routes
│   ├── auth.js         # Authentication routes
│   ├── users.js        # User management
│   ├── patients.js     # Patient management
│   └── health.js       # Health check routes
├── logs/               # Log files
├── uploads/            # File uploads
├── .env               # Environment variables
├── server.js          # Main server file
├── package.json       # Dependencies
└── README.md         # This file
```

## 🔒 Security Features

- **JWT Authentication** - Secure token-based auth with refresh tokens
- **Password Hashing** - Bcrypt with salt rounds
- **Rate Limiting** - API abuse prevention
- **CORS Protection** - Controlled cross-origin access
- **Helmet Security** - Security headers
- **Input Validation** - Express-validator for request validation
- **Account Locking** - Failed login attempt protection
- **Role-based Access** - Granular permission control

## 📊 Monitoring & Health Checks

### Health Endpoints
- `/health` - Basic application health
- `/health/detailed` - Comprehensive system status
- `/health/ready` - Kubernetes readiness probe
- `/health/live` - Kubernetes liveness probe

### Logging
- **Winston** for structured logging
- **Morgan** for HTTP request logging
- Log levels: error, warn, info, debug
- Separate error and combined log files

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

### Environment Setup
1. Set `NODE_ENV=production`
2. Use strong JWT secrets
3. Configure proper CORS origins
4. Set up SSL/HTTPS
5. Configure MongoDB Atlas IP whitelist
6. Set up proper logging

### Docker Deployment
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### Environment Variables for Production
```env
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb+srv://...
JWT_SECRET=your-production-secret
CORS_ORIGIN=https://yourdomain.com
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, email <EMAIL> or create an issue in the repository.
