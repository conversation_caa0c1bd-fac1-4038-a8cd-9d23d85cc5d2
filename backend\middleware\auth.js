import jwt from 'jsonwebtoken';
import User from '../models/User.js';
import logger from '../config/logger.js';

// Middleware to verify JWT token
export const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'Access token is required',
                error: 'MISSING_TOKEN'
            });
        }

        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Get user from database
        const user = await User.findById(decoded.userId).select('-password');
        
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid token - user not found',
                error: 'INVALID_TOKEN'
            });
        }

        if (!user.isActive) {
            return res.status(401).json({
                success: false,
                message: 'Account is deactivated',
                error: 'ACCOUNT_DEACTIVATED'
            });
        }

        // Add user to request object
        req.user = user;
        next();
    } catch (error) {
        logger.error('Authentication error:', error);
        
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                message: 'Invalid token',
                error: 'INVALID_TOKEN'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                message: 'Token expired',
                error: 'TOKEN_EXPIRED'
            });
        }

        res.status(500).json({
            success: false,
            message: 'Authentication failed',
            error: 'AUTH_ERROR'
        });
    }
};

// Middleware to check user roles
export const authorizeRoles = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required',
                error: 'NOT_AUTHENTICATED'
            });
        }

        if (!roles.includes(req.user.role)) {
            logger.warn(`Access denied for user ${req.user._id} with role ${req.user.role}. Required roles: ${roles.join(', ')}`);
            
            return res.status(403).json({
                success: false,
                message: 'Insufficient permissions',
                error: 'INSUFFICIENT_PERMISSIONS',
                required: roles,
                current: req.user.role
            });
        }

        next();
    };
};

// Middleware to check if user owns resource or has admin privileges
export const authorizeOwnerOrAdmin = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: 'Authentication required',
            error: 'NOT_AUTHENTICATED'
        });
    }

    const resourceUserId = req.params.userId || req.params.id;
    const isOwner = req.user._id.toString() === resourceUserId;
    const isAdmin = req.user.role === 'admin';

    if (!isOwner && !isAdmin) {
        logger.warn(`Access denied for user ${req.user._id}. Not owner of resource ${resourceUserId} and not admin`);
        
        return res.status(403).json({
            success: false,
            message: 'Access denied - you can only access your own resources',
            error: 'ACCESS_DENIED'
        });
    }

    next();
};
