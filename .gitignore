# AfyaSecure - Health Management System
# Gitignore for frontend (React/TypeScript) and backend (Node.js/MongoDB)

# Dependencies
frontend/node_modules/
backend/node_modules/
node_modules/

# Production builds
frontend/dist/
frontend/build/
backend/dist/
backend/build/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
frontend/.env*
backend/.env*

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
backend/logs/*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Upload directories
backend/uploads/*
!backend/uploads/.gitkeep

# Database
*.sqlite
*.db

# Testing
/coverage

# Vercel
.vercel