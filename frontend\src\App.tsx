import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RouterProvider, Navigate } from "react-router-dom"
import { AuthProvider } from "./contexts/AuthContext"
import { ToastProvider } from "./contexts/ToastContext"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import ErrorBoundary from "./components/ErrorBoundary"
import LoadingSpinner from "./components/LoadingSpinner"
import { lazy, Suspense } from "react"

// Lazy load components for better performance
const Login = lazy(() => import("./pages/auth/Login"))
const Dashboard = lazy(() => import("./pages/Dashboard"))
const PatientRecords = lazy(() => import("./pages/PatientRecords"))
const Appointments = lazy(() => import("./pages/Appointments"))
const FacilityDirectory = lazy(() => import("./pages/FacilityDirectory"))
const Billing = lazy(() => import("./pages/Billing"))
const AdminDashboard = lazy(() => import("./pages/AdminDashboard"))
const Profile = lazy(() => import("./pages/Profile"))
const NotFound = lazy(() => import("./pages/NotFound"))
const ProtectedRoute = lazy(() => import("./components/ProtectedRoute"))

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
  },
})

// Create router configuration
const router = createBrowserRouter([
  {
    path: "/",
    element: <Navigate to="/dashboard" replace />,
  },
  {
    path: "/login",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <Login />
      </Suspense>
    ),
  },
  {
    path: "/dashboard",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <ProtectedRoute>
          <Dashboard />
        </ProtectedRoute>
      </Suspense>
    ),
  },
  {
    path: "/patients",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <ProtectedRoute roles={["doctor", "admin"]}>
          <PatientRecords />
        </ProtectedRoute>
      </Suspense>
    ),
    children: [
      {
        path: ":id",
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <PatientRecords />
          </Suspense>
        ),
      },
    ],
  },
  {
    path: "/appointments",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <ProtectedRoute>
          <Appointments />
        </ProtectedRoute>
      </Suspense>
    ),
    children: [
      {
        path: "new",
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <Appointments />
          </Suspense>
        ),
      },
      {
        path: ":id",
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <Appointments />
          </Suspense>
        ),
      },
    ],
  },
  {
    path: "/facilities",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <ProtectedRoute>
          <FacilityDirectory />
        </ProtectedRoute>
      </Suspense>
    ),
    children: [
      {
        path: ":id",
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <FacilityDirectory />
          </Suspense>
        ),
      },
    ],
  },
  {
    path: "/billing",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <ProtectedRoute roles={["doctor", "admin"]}>
          <Billing />
        </ProtectedRoute>
      </Suspense>
    ),
    children: [
      {
        path: ":id",
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            <Billing />
          </Suspense>
        ),
      },
    ],
  },
  {
    path: "/admin",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <ProtectedRoute roles={["admin"]}>
          <AdminDashboard />
        </ProtectedRoute>
      </Suspense>
    ),
  },
  {
    path: "/profile",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <ProtectedRoute>
          <Profile />
        </ProtectedRoute>
      </Suspense>
    ),
  },
  {
    path: "*",
    element: (
      <Suspense fallback={<LoadingSpinner />}>
        <NotFound />
      </Suspense>
    ),
  },
])

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <ToastProvider>
            <div className="min-h-screen bg-gray-50">
              <RouterProvider router={router} />
            </div>
            <ReactQueryDevtools initialIsOpen={false} />
          </ToastProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  )
}

export default App
