import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import Layout from '../components/Layout'
import { useToast } from '../contexts/ToastContext'
import { facilityAPI } from '../services/api'
import { 
  BuildingOfficeIcon,
  MapPinIcon,
  PhoneIcon,
  ClockIcon,
  UserGroupIcon,
  MagnifyingGlassIcon,
  StarIcon
} from '@heroicons/react/24/outline'

interface Facility {
  id: string
  name: string
  type: string
  address: {
    street: string
    city: string
    state: string
    zipCode: string
  }
  phone: string
  email: string
  website?: string
  services: string[]
  doctors: Array<{
    id: string
    name: string
    specialization: string
    available: boolean
  }>
  operatingHours: {
    [key: string]: {
      open: string
      close: string
      closed?: boolean
    }
  }
  rating: number
  reviewCount: number
  coordinates?: {
    lat: number
    lng: number
  }
}

const FacilityDirectory: React.FC = () => {
  const { id } = useParams()
  const { addToast } = useToast()
  
  const [facilities, setFacilities] = useState<Facility[]>([])
  const [selectedFacility, setSelectedFacility] = useState<Facility | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')

  useEffect(() => {
    fetchFacilities()
  }, [])

  useEffect(() => {
    if (id && facilities.length > 0) {
      const facility = facilities.find(f => f.id === id)
      setSelectedFacility(facility || null)
    }
  }, [id, facilities])

  const fetchFacilities = async () => {
    try {
      const data = await facilityAPI.getFacilities()
      setFacilities(data)
    } catch (error) {
      addToast({
        title: 'Error',
        description: 'Failed to fetch facilities',
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredFacilities = facilities.filter(facility => {
    const matchesSearch = facility.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         facility.services.some(service => 
                           service.toLowerCase().includes(searchTerm.toLowerCase())
                         )
    const matchesType = filterType === 'all' || facility.type === filterType
    return matchesSearch && matchesType
  })

  const facilityTypes = ['all', 'hospital', 'clinic', 'pharmacy', 'laboratory', 'specialist']

  if (loading) {
    return (
      <Layout>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-300 rounded w-1/4"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1 space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-300 rounded"></div>
              ))}
            </div>
            <div className="lg:col-span-2">
              <div className="h-96 bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Health Facility Directory</h1>
          <p className="mt-1 text-sm text-gray-600">
            Find healthcare facilities and services in your area
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search facilities or services..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
            >
              {facilityTypes.map(type => (
                <option key={type} value={type}>
                  {type === 'all' ? 'All Types' : type.charAt(0).toUpperCase() + type.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Facility List */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  Facilities ({filteredFacilities.length})
                </h3>
              </div>
              
              <div className="max-h-96 overflow-y-auto">
                {filteredFacilities.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    <BuildingOfficeIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                    <p>No facilities found</p>
                  </div>
                ) : (
                  filteredFacilities.map((facility) => (
                    <div
                      key={facility.id}
                      onClick={() => setSelectedFacility(facility)}
                      className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                        selectedFacility?.id === facility.id ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                    >
                      <div className="space-y-2">
                        <div className="flex items-start justify-between">
                          <h4 className="font-medium text-gray-900">{facility.name}</h4>
                          <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                            {facility.type}
                          </span>
                        </div>
                        
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPinIcon className="h-4 w-4 mr-1" />
                          <span>{facility.address.city}, {facility.address.state}</span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-sm text-gray-600">
                            <StarIcon className="h-4 w-4 mr-1 text-yellow-400 fill-current" />
                            <span>{facility.rating} ({facility.reviewCount})</span>
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <UserGroupIcon className="h-4 w-4 mr-1" />
                            <span>{facility.doctors.length} doctors</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Facility Details */}
          <div className="lg:col-span-2">
            {selectedFacility ? (
              <div className="space-y-6">
                {/* Facility Info Card */}
                <div className="bg-white shadow rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">{selectedFacility.name}</h2>
                      <p className="text-gray-600 capitalize">{selectedFacility.type}</p>
                      <div className="flex items-center mt-2">
                        <StarIcon className="h-5 w-5 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm text-gray-600">
                          {selectedFacility.rating} ({selectedFacility.reviewCount} reviews)
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex items-start">
                        <MapPinIcon className="h-5 w-5 text-gray-400 mr-2 mt-0.5" />
                        <div>
                          <p>{selectedFacility.address.street}</p>
                          <p>{selectedFacility.address.city}, {selectedFacility.address.state} {selectedFacility.address.zipCode}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center">
                        <PhoneIcon className="h-5 w-5 text-gray-400 mr-2" />
                        <span>{selectedFacility.phone}</span>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Operating Hours</h4>
                      <div className="space-y-1 text-sm">
                        {Object.entries(selectedFacility.operatingHours).map(([day, hours]) => (
                          <div key={day} className="flex justify-between">
                            <span className="capitalize">{day}:</span>
                            <span>
                              {hours.closed ? 'Closed' : `${hours.open} - ${hours.close}`}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Services */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Services</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {selectedFacility.services.map((service, index) => (
                      <div
                        key={index}
                        className="px-3 py-2 bg-blue-50 text-blue-800 text-sm rounded-lg text-center"
                      >
                        {service}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Doctors */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Available Doctors</h3>
                  <div className="space-y-3">
                    {selectedFacility.doctors.map((doctor) => (
                      <div
                        key={doctor.id}
                        className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                      >
                        <div>
                          <h4 className="font-medium text-gray-900">{doctor.name}</h4>
                          <p className="text-sm text-gray-600">{doctor.specialization}</p>
                        </div>
                        <div className="flex items-center">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            doctor.available 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {doctor.available ? 'Available' : 'Busy'}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg p-12 text-center">
                <BuildingOfficeIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Facility</h3>
                <p className="text-gray-600">Choose a facility from the list to view details</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default FacilityDirectory
