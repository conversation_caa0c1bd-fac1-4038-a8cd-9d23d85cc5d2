import type React from "react"
import { create<PERSON>ontext, use<PERSON>ontex<PERSON>, useReducer, useEffect } from "react"
import { authAPI } from "../services/api"

interface User {
  id: string
  email: string
  role: "patient" | "doctor" | "admin"
  firstName: string
  lastName: string
  profilePhoto?: string
  verified: boolean
}

interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  loading: boolean
  error: string | null
}

type AuthAction =
  | { type: "LOGIN_START" }
  | { type: "LOGIN_SUCCESS"; payload: { user: User; token: string; refreshToken: string } }
  | { type: "LOGIN_FAILURE"; payload: string }
  | { type: "LOGOUT" }
  | { type: "REFRESH_TOKEN"; payload: string }
  | { type: "UPDATE_USER"; payload: User }

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem("token"),
  refreshToken: localStorage.getItem("refreshToken"),
  loading: false,
  error: null,
}

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case "LOGIN_START":
      return { ...state, loading: true, error: null }
    case "LOGIN_SUCCESS":
      localStorage.setItem("token", action.payload.token)
      localStorage.setItem("refreshToken", action.payload.refreshToken)
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        loading: false,
        error: null,
      }
    case "LOGIN_FAILURE":
      return { ...state, loading: false, error: action.payload }
    case "LOGOUT":
      localStorage.removeItem("token")
      localStorage.removeItem("refreshToken")
      return { ...initialState, token: null, refreshToken: null }
    case "REFRESH_TOKEN":
      localStorage.setItem("token", action.payload)
      return { ...state, token: action.payload }
    case "UPDATE_USER":
      return { ...state, user: action.payload }
    default:
      return state
  }
}

const AuthContext = createContext<{
  state: AuthState
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
}>({
  state: initialState,
  login: async () => {},
  logout: () => {},
  refreshToken: async () => {},
})

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState)

  const login = async (email: string, password: string) => {
    dispatch({ type: "LOGIN_START" })
    try {
      const response = await authAPI.login(email, password)
      dispatch({
        type: "LOGIN_SUCCESS",
        payload: {
          user: response.user,
          token: response.token,
          refreshToken: response.refreshToken,
        },
      })
    } catch (error: any) {
      dispatch({ type: "LOGIN_FAILURE", payload: error.message })
    }
  }

  const logout = () => {
    dispatch({ type: "LOGOUT" })
  }

  const refreshToken = async () => {
    try {
      if (state.refreshToken) {
        const response = await authAPI.refreshToken(state.refreshToken)
        dispatch({ type: "REFRESH_TOKEN", payload: response.token })
      }
    } catch (error) {
      logout()
    }
  }

  useEffect(() => {
    if (state.token) {
      authAPI
        .getCurrentUser()
        .then((user) => {
          dispatch({ type: "UPDATE_USER", payload: user })
        })
        .catch(() => {
          logout()
        })
    }
  }, [state.token])

  return <AuthContext.Provider value={{ state, login, logout, refreshToken }}>{children}</AuthContext.Provider>
}

export const useAuth = () => useContext(AuthContext)
