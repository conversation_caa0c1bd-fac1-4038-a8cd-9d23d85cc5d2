import express from 'express';
import { healthCheck } from '../config/database.js';
import logger from '../config/logger.js';

const router = express.Router();

// Health check endpoint
router.get('/', async (req, res) => {
    try {
        const dbHealth = await healthCheck();
        
        const healthStatus = {
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            application: {
                name: process.env.APP_NAME || 'AfyaSecure API',
                version: process.env.APP_VERSION || '1.0.0',
                environment: process.env.NODE_ENV || 'development',
                nodeVersion: process.version
            },
            database: dbHealth,
            memory: {
                used: process.memoryUsage().heapUsed,
                total: process.memoryUsage().heapTotal,
                external: process.memoryUsage().external,
                rss: process.memoryUsage().rss
            },
            system: {
                platform: process.platform,
                arch: process.arch,
                cpus: require('os').cpus().length,
                loadavg: require('os').loadavg(),
                freemem: require('os').freemem(),
                totalmem: require('os').totalmem()
            }
        };

        // Determine overall health status
        const isHealthy = dbHealth.status === 'healthy';
        const statusCode = isHealthy ? 200 : 503;
        
        if (!isHealthy) {
            healthStatus.status = 'degraded';
            logger.warn('Health check failed', { dbHealth });
        }

        res.status(statusCode).json(healthStatus);
    } catch (error) {
        logger.error('Health check error:', error);
        
        res.status(503).json({
            status: 'error',
            timestamp: new Date().toISOString(),
            message: 'Health check failed',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Detailed health check for monitoring systems
router.get('/detailed', async (req, res) => {
    try {
        const dbHealth = await healthCheck();
        
        // Additional checks can be added here
        const checks = {
            database: {
                status: dbHealth.status,
                responseTime: Date.now(), // You can measure actual response time
                details: dbHealth
            },
            // Add more service checks here
            // redis: { status: 'healthy' },
            // externalApi: { status: 'healthy' }
        };

        const overallStatus = Object.values(checks).every(check => check.status === 'healthy') 
            ? 'healthy' 
            : 'unhealthy';

        const detailedHealth = {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            checks,
            metadata: {
                uptime: process.uptime(),
                version: process.env.APP_VERSION || '1.0.0',
                environment: process.env.NODE_ENV || 'development'
            }
        };

        const statusCode = overallStatus === 'healthy' ? 200 : 503;
        res.status(statusCode).json(detailedHealth);
    } catch (error) {
        logger.error('Detailed health check error:', error);
        
        res.status(503).json({
            status: 'error',
            timestamp: new Date().toISOString(),
            message: 'Detailed health check failed',
            error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
        });
    }
});

// Readiness probe (for Kubernetes)
router.get('/ready', async (req, res) => {
    try {
        const dbHealth = await healthCheck();
        
        if (dbHealth.status === 'healthy') {
            res.status(200).json({
                status: 'ready',
                timestamp: new Date().toISOString()
            });
        } else {
            res.status(503).json({
                status: 'not ready',
                timestamp: new Date().toISOString(),
                reason: 'Database not healthy'
            });
        }
    } catch (error) {
        logger.error('Readiness check error:', error);
        res.status(503).json({
            status: 'not ready',
            timestamp: new Date().toISOString(),
            reason: 'Health check failed'
        });
    }
});

// Liveness probe (for Kubernetes)
router.get('/live', (req, res) => {
    res.status(200).json({
        status: 'alive',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

export default router;
