import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import Layout from '../components/Layout'
import { useAuth } from '../contexts/AuthContext'
import { useToast } from '../contexts/ToastContext'
import { patientAPI } from '../services/api'
import { 
  UserIcon, 
  PlusIcon, 
  MagnifyingGlassIcon,
  DocumentTextIcon,
  HeartIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline'

interface Patient {
  id: string
  personalInfo: {
    firstName: string
    lastName: string
    dateOfBirth: string
    gender: string
    phone: string
    email: string
    address: string
  }
  medicalHistory: Array<{
    condition: string
    diagnosedDate: string
    status: string
  }>
  allergies: string[]
  medications: Array<{
    name: string
    dosage: string
    frequency: string
  }>
  lastVisit?: string
  doctorId: string
}

const PatientRecords: React.FC = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const { state } = useAuth()
  const { addToast } = useToast()
  
  const [patients, setPatients] = useState<Patient[]>([])
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddModal, setShowAddModal] = useState(false)

  useEffect(() => {
    fetchPatients()
  }, [])

  useEffect(() => {
    if (id && patients.length > 0) {
      const patient = patients.find(p => p.id === id)
      setSelectedPatient(patient || null)
    }
  }, [id, patients])

  const fetchPatients = async () => {
    try {
      const data = await patientAPI.getPatients()
      setPatients(data)
    } catch (error) {
      addToast({
        title: 'Error',
        description: 'Failed to fetch patient records',
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredPatients = patients.filter(patient =>
    `${patient.personalInfo.firstName} ${patient.personalInfo.lastName}`
      .toLowerCase()
      .includes(searchTerm.toLowerCase()) ||
    patient.personalInfo.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handlePatientSelect = (patient: Patient) => {
    setSelectedPatient(patient)
    navigate(`/patients/${patient.id}`)
  }

  if (loading) {
    return (
      <Layout>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-300 rounded w-1/4"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1 space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-300 rounded"></div>
              ))}
            </div>
            <div className="lg:col-span-2">
              <div className="h-96 bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Patient Records</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage patient information and medical records
            </p>
          </div>
          {(state.user?.role === 'doctor' || state.user?.role === 'admin') && (
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center space-x-2"
            >
              <PlusIcon className="h-5 w-5" />
              <span>Add Patient</span>
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Patient List */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg">
              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search patients..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="max-h-96 overflow-y-auto">
                {filteredPatients.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    <UserIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                    <p>No patients found</p>
                  </div>
                ) : (
                  filteredPatients.map((patient) => (
                    <div
                      key={patient.id}
                      onClick={() => handlePatientSelect(patient)}
                      className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                        selectedPatient?.id === patient.id ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center">
                            <UserIcon className="h-6 w-6 text-gray-600" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {patient.personalInfo.firstName} {patient.personalInfo.lastName}
                          </p>
                          <p className="text-sm text-gray-500 truncate">
                            {patient.personalInfo.email}
                          </p>
                          {patient.lastVisit && (
                            <p className="text-xs text-gray-400">
                              Last visit: {new Date(patient.lastVisit).toLocaleDateString()}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Patient Details */}
          <div className="lg:col-span-2">
            {selectedPatient ? (
              <div className="space-y-6">
                {/* Patient Info Card */}
                <div className="bg-white shadow rounded-lg p-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="h-16 w-16 bg-gray-300 rounded-full flex items-center justify-center">
                      <UserIcon className="h-8 w-8 text-gray-600" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">
                        {selectedPatient.personalInfo.firstName} {selectedPatient.personalInfo.lastName}
                      </h2>
                      <p className="text-gray-600">{selectedPatient.personalInfo.email}</p>
                      <p className="text-sm text-gray-500">
                        Born: {new Date(selectedPatient.personalInfo.dateOfBirth).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">Gender:</span>
                      <span className="ml-2 text-gray-900">{selectedPatient.personalInfo.gender}</span>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Phone:</span>
                      <span className="ml-2 text-gray-900">{selectedPatient.personalInfo.phone}</span>
                    </div>
                    <div className="col-span-2">
                      <span className="font-medium text-gray-700">Address:</span>
                      <span className="ml-2 text-gray-900">{selectedPatient.personalInfo.address}</span>
                    </div>
                  </div>
                </div>

                {/* Medical Information Tabs */}
                <div className="bg-white shadow rounded-lg">
                  <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8 px-6">
                      <button className="border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600">
                        <HeartIcon className="h-5 w-5 inline mr-2" />
                        Medical History
                      </button>
                      <button className="border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                        <DocumentTextIcon className="h-5 w-5 inline mr-2" />
                        Medications
                      </button>
                      <button className="border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700">
                        <ClipboardDocumentListIcon className="h-5 w-5 inline mr-2" />
                        Visit Notes
                      </button>
                    </nav>
                  </div>
                  
                  <div className="p-6">
                    {/* Medical History */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium text-gray-900">Medical History</h3>
                      {selectedPatient.medicalHistory.length === 0 ? (
                        <p className="text-gray-500">No medical history recorded</p>
                      ) : (
                        <div className="space-y-3">
                          {selectedPatient.medicalHistory.map((condition, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h4 className="font-medium text-gray-900">{condition.condition}</h4>
                                  <p className="text-sm text-gray-600">
                                    Diagnosed: {new Date(condition.diagnosedDate).toLocaleDateString()}
                                  </p>
                                </div>
                                <span className={`px-2 py-1 text-xs rounded-full ${
                                  condition.status === 'active' 
                                    ? 'bg-red-100 text-red-800' 
                                    : 'bg-green-100 text-green-800'
                                }`}>
                                  {condition.status}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Allergies */}
                    <div className="mt-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-3">Allergies</h3>
                      {selectedPatient.allergies.length === 0 ? (
                        <p className="text-gray-500">No known allergies</p>
                      ) : (
                        <div className="flex flex-wrap gap-2">
                          {selectedPatient.allergies.map((allergy, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full"
                            >
                              {allergy}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg p-12 text-center">
                <UserIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Patient</h3>
                <p className="text-gray-600">Choose a patient from the list to view their records</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default PatientRecords
