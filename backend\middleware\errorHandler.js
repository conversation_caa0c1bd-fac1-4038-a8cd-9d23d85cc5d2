import logger from '../config/logger.js';

export const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;

    // Log error
    logger.error('Error Handler:', {
        message: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });

    // Mongoose bad ObjectId
    if (err.name === 'CastError') {
        const message = 'Invalid resource ID format';
        error = {
            message,
            statusCode: 400,
            error: 'INVALID_ID_FORMAT'
        };
    }

    // Mongoose duplicate key
    if (err.code === 11000) {
        const field = Object.keys(err.keyValue)[0];
        const message = `${field} already exists`;
        error = {
            message,
            statusCode: 400,
            error: 'DUPLICATE_FIELD',
            field
        };
    }

    // Mongoose validation error
    if (err.name === 'ValidationError') {
        const message = Object.values(err.errors).map(val => val.message).join(', ');
        error = {
            message,
            statusCode: 400,
            error: 'VALIDATION_ERROR',
            details: Object.values(err.errors).map(val => ({
                field: val.path,
                message: val.message,
                value: val.value
            }))
        };
    }

    // JWT errors
    if (err.name === 'JsonWebTokenError') {
        error = {
            message: 'Invalid token',
            statusCode: 401,
            error: 'INVALID_TOKEN'
        };
    }

    if (err.name === 'TokenExpiredError') {
        error = {
            message: 'Token expired',
            statusCode: 401,
            error: 'TOKEN_EXPIRED'
        };
    }

    // Express validator errors
    if (err.type === 'entity.parse.failed') {
        error = {
            message: 'Invalid JSON format',
            statusCode: 400,
            error: 'INVALID_JSON'
        };
    }

    // File upload errors
    if (err.code === 'LIMIT_FILE_SIZE') {
        error = {
            message: 'File too large',
            statusCode: 400,
            error: 'FILE_TOO_LARGE'
        };
    }

    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
        error = {
            message: 'Unexpected file field',
            statusCode: 400,
            error: 'UNEXPECTED_FILE'
        };
    }

    // Default error response
    const statusCode = error.statusCode || 500;
    const message = error.message || 'Internal Server Error';
    const errorCode = error.error || 'INTERNAL_ERROR';

    const response = {
        success: false,
        message,
        error: errorCode,
        ...(error.details && { details: error.details }),
        ...(error.field && { field: error.field }),
        timestamp: new Date().toISOString(),
        path: req.path,
        method: req.method
    };

    // Include stack trace in development
    if (process.env.NODE_ENV === 'development') {
        response.stack = err.stack;
    }

    // Include request ID if available
    if (req.id) {
        response.requestId = req.id;
    }

    res.status(statusCode).json(response);
};
