import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import Layout from '../components/Layout'
import { useAuth } from '../contexts/AuthContext'
import { useToast } from '../contexts/ToastContext'
import { billingAPI } from '../services/api'
import { 
  CreditCardIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  PlusIcon,
  EyeIcon,
  PrinterIcon
} from '@heroicons/react/24/outline'

interface BillItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  total: number
}

interface Bill {
  id: string
  patientId: string
  patientName: string
  doctorId: string
  doctorName: string
  facilityId: string
  facilityName: string
  items: BillItem[]
  subtotal: number
  tax: number
  total: number
  status: 'draft' | 'pending' | 'approved' | 'paid' | 'overdue'
  createdDate: string
  dueDate: string
  paidDate?: string
  notes?: string
  approvedBy?: string
  approvedDate?: string
}

const Billing: React.FC = () => {
  const { id } = useParams()
  const { state } = useAuth()
  const { addToast } = useToast()
  
  const [bills, setBills] = useState<Bill[]>([])
  const [selectedBill, setSelectedBill] = useState<Bill | null>(null)
  const [loading, setLoading] = useState(true)
  const [filterStatus, setFilterStatus] = useState('all')
  const [showCreateModal, setShowCreateModal] = useState(false)

  useEffect(() => {
    fetchBills()
  }, [])

  useEffect(() => {
    if (id && bills.length > 0) {
      const bill = bills.find(b => b.id === id)
      setSelectedBill(bill || null)
    }
  }, [id, bills])

  const fetchBills = async () => {
    try {
      const data = await billingAPI.getBills()
      setBills(data)
    } catch (error) {
      addToast({
        title: 'Error',
        description: 'Failed to fetch billing records',
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleApproveBill = async (billId: string) => {
    try {
      await billingAPI.approveBill(billId)
      addToast({
        title: 'Success',
        description: 'Bill approved successfully',
        type: 'success'
      })
      fetchBills()
    } catch (error) {
      addToast({
        title: 'Error',
        description: 'Failed to approve bill',
        type: 'error'
      })
    }
  }

  const handleGenerateInvoice = async (billId: string) => {
    try {
      const invoiceData = await billingAPI.generateInvoice(billId)
      // Handle invoice download/display
      addToast({
        title: 'Success',
        description: 'Invoice generated successfully',
        type: 'success'
      })
    } catch (error) {
      addToast({
        title: 'Error',
        description: 'Failed to generate invoice',
        type: 'error'
      })
    }
  }

  const filteredBills = bills.filter(bill => 
    filterStatus === 'all' || bill.status === filterStatus
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
      case 'paid':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />
      case 'overdue':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      default:
        return <DocumentTextIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'draft':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <Layout>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-300 rounded w-1/4"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1 space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-300 rounded"></div>
              ))}
            </div>
            <div className="lg:col-span-2">
              <div className="h-96 bg-gray-300 rounded"></div>
            </div>
          </div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Billing & Invoices</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage billing records and generate invoices
            </p>
          </div>
          {(state.user?.role === 'doctor' || state.user?.role === 'admin') && (
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center space-x-2"
            >
              <PlusIcon className="h-5 w-5" />
              <span>Create Bill</span>
            </button>
          )}
        </div>

        {/* Filter */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Filter by status:</label>
            <select
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="draft">Draft</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="paid">Paid</option>
              <option value="overdue">Overdue</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Bills List */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">
                  Bills ({filteredBills.length})
                </h3>
              </div>
              
              <div className="max-h-96 overflow-y-auto">
                {filteredBills.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    <CreditCardIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                    <p>No bills found</p>
                  </div>
                ) : (
                  filteredBills.map((bill) => (
                    <div
                      key={bill.id}
                      onClick={() => setSelectedBill(bill)}
                      className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                        selectedBill?.id === bill.id ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                    >
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="font-medium text-gray-900">#{bill.id.slice(-6)}</span>
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(bill.status)}`}>
                            {bill.status}
                          </span>
                        </div>
                        
                        <div>
                          <p className="text-sm font-medium text-gray-900">{bill.patientName}</p>
                          <p className="text-sm text-gray-600">{bill.facilityName}</p>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-lg font-bold text-gray-900">
                            ${bill.total.toFixed(2)}
                          </span>
                          <span className="text-sm text-gray-500">
                            {new Date(bill.createdDate).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Bill Details */}
          <div className="lg:col-span-2">
            {selectedBill ? (
              <div className="space-y-6">
                {/* Bill Header */}
                <div className="bg-white shadow rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">
                        Invoice #{selectedBill.id.slice(-6)}
                      </h2>
                      <div className="flex items-center mt-2">
                        {getStatusIcon(selectedBill.status)}
                        <span className={`ml-2 px-2 py-1 text-sm rounded-full ${getStatusColor(selectedBill.status)}`}>
                          {selectedBill.status.charAt(0).toUpperCase() + selectedBill.status.slice(1)}
                        </span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleGenerateInvoice(selectedBill.id)}
                        className="flex items-center px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                      >
                        <PrinterIcon className="h-4 w-4 mr-1" />
                        Print
                      </button>
                      {selectedBill.status === 'pending' && (state.user?.role === 'admin') && (
                        <button
                          onClick={() => handleApproveBill(selectedBill.id)}
                          className="flex items-center px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
                        >
                          <CheckCircleIcon className="h-4 w-4 mr-1" />
                          Approve
                        </button>
                      )}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-6 text-sm">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Bill To:</h4>
                      <p className="text-gray-900">{selectedBill.patientName}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">From:</h4>
                      <p className="text-gray-900">{selectedBill.facilityName}</p>
                      <p className="text-gray-600">Dr. {selectedBill.doctorName}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Dates:</h4>
                      <p className="text-gray-600">Created: {new Date(selectedBill.createdDate).toLocaleDateString()}</p>
                      <p className="text-gray-600">Due: {new Date(selectedBill.dueDate).toLocaleDateString()}</p>
                      {selectedBill.paidDate && (
                        <p className="text-gray-600">Paid: {new Date(selectedBill.paidDate).toLocaleDateString()}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Bill Items */}
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Items</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Description
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Qty
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Unit Price
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {selectedBill.items.map((item) => (
                          <tr key={item.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {item.description}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {item.quantity}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              ${item.unitPrice.toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              ${item.total.toFixed(2)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  
                  {/* Totals */}
                  <div className="mt-6 flex justify-end">
                    <div className="w-64 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Subtotal:</span>
                        <span className="text-gray-900">${selectedBill.subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Tax:</span>
                        <span className="text-gray-900">${selectedBill.tax.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-lg font-bold border-t pt-2">
                        <span className="text-gray-900">Total:</span>
                        <span className="text-gray-900">${selectedBill.total.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Notes */}
                {selectedBill.notes && (
                  <div className="bg-white shadow rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Notes</h3>
                    <p className="text-gray-600">{selectedBill.notes}</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg p-12 text-center">
                <CreditCardIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Bill</h3>
                <p className="text-gray-600">Choose a bill from the list to view details</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  )
}

export default Billing
