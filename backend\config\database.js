import mongoose from 'mongoose';
import logger from './logger.js';

const connectDB = async () => {
    try {
        const mongoURI = process.env.MONGODB_URI;
        
        if (!mongoURI) {
            throw new Error('MongoDB URI is not defined in environment variables');
        }

        // MongoDB connection options
        const options = {
            maxPoolSize: parseInt(process.env.DB_MAX_POOL_SIZE) || 10,
            minPoolSize: parseInt(process.env.DB_MIN_POOL_SIZE) || 5,
            maxIdleTimeMS: 30000,
            serverSelectionTimeoutMS: 5000,
            socketTimeoutMS: 45000,
            bufferCommands: false,
            bufferMaxEntries: 0,
        };

        // Connect to MongoDB Atlas
        const conn = await mongoose.connect(mongoURI, options);

        logger.info(`✅ MongoDB Atlas connected: ${conn.connection.host}`);
        logger.info(`📊 Database: ${conn.connection.name}`);

        // Connection event listeners
        mongoose.connection.on('connected', () => {
            logger.info('🔗 Mongoose connected to MongoDB Atlas');
        });

        mongoose.connection.on('error', (err) => {
            logger.error('❌ Mongoose connection error:', err);
        });

        mongoose.connection.on('disconnected', () => {
            logger.warn('⚠️  Mongoose disconnected from MongoDB Atlas');
        });

        // Handle application termination
        process.on('SIGINT', async () => {
            await mongoose.connection.close();
            logger.info('🔌 MongoDB Atlas connection closed through app termination');
            process.exit(0);
        });

    } catch (error) {
        logger.error('❌ MongoDB Atlas connection failed:', error.message);
        
        if (process.env.NODE_ENV === 'development') {
            logger.error('Full error details:', error);
        }
        
        // Exit process with failure
        process.exit(1);
    }
};

// Export connection status check
export const isConnected = () => {
    return mongoose.connection.readyState === 1;
};

// Export database health check
export const healthCheck = async () => {
    try {
        if (!isConnected()) {
            throw new Error('Database not connected');
        }

        // Ping the database
        await mongoose.connection.db.admin().ping();
        
        return {
            status: 'healthy',
            database: mongoose.connection.name,
            host: mongoose.connection.host,
            readyState: mongoose.connection.readyState,
            collections: Object.keys(mongoose.connection.collections).length,
        };
    } catch (error) {
        return {
            status: 'unhealthy',
            error: error.message,
            readyState: mongoose.connection.readyState,
        };
    }
};

export default connectDB;
