import logger from '../config/logger.js';

export const notFound = (req, res, next) => {
    // Log the 404 attempt
    logger.warn('404 Not Found:', {
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
    });

    const error = new Error(`Route not found - ${req.originalUrl}`);
    error.statusCode = 404;
    error.error = 'ROUTE_NOT_FOUND';
    
    next(error);
};
